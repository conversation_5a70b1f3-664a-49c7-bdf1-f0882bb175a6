/**
 * Configuración para Google Drive API Integration
 *
 * INSTRUCCIONES DE CONFIGURACIÓN:
 * 1. Ve a https://console.cloud.google.com/
 * 2. Crea un nuevo proyecto o selecciona uno existente
 * 3. Habilita Google Drive API en APIs & Services > Library
 * 4. Crea credenciales OAuth 2.0 en APIs & Services > Credentials
 * 5. Configura los dominios autorizados
 * 6. Reemplaza CLIENT_ID con tu Client ID real
 */

const GOOGLE_DRIVE_CONFIG = {
  // ⚠️ REEMPLAZA ESTE CLIENT_ID CON EL TUYO
  CLIENT_ID: 'TU_CLIENT_ID_AQUI.apps.googleusercontent.com',

  // URLs de Google APIs
  DISCOVERY_DOC: 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest',
  SCOPES: 'https://www.googleapis.com/auth/drive.file',

  // Configuración de carpetas en Google Drive
  FOLDER_CONFIG: {
    BASE_FOLDER_NAME: 'Slideshow Images',
    CREATE_DATE_FOLDERS: true,
    CREATE_SEMINAR_FOLDERS: true,
    FOLDER_PERMISSIONS: 'private' // 'private' | 'public' | 'domain'
  },

  // Configuración de archivos
  FILE_CONFIG: {
    ALLOWED_TYPES: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/gif'
    ],
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    COMPRESSION_QUALITY: 0.8,
    MAX_DIMENSION: 1920,
    THUMBNAIL_SIZE: 300
  },

  // Configuración de subida
  UPLOAD_CONFIG: {
    MAX_CONCURRENT_UPLOADS: 3,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // ms
    CHUNK_SIZE: 256 * 1024, // 256KB chunks para archivos grandes
    SHOW_PROGRESS: true
  },

  // Configuración de cache
  CACHE_CONFIG: {
    ENABLED: true,
    MAX_SIZE_MB: 50,
    TTL_HOURS: 24,
    STORAGE_KEY: 'slideshow_drive_cache'
  },

  // Mensajes de usuario
  MESSAGES: {
    AUTH_REQUIRED: 'Necesitas autorizar el acceso a Google Drive para subir imágenes.',
    UPLOAD_SUCCESS: 'Imagen subida exitosamente',
    UPLOAD_ERROR: 'Error al subir la imagen',
    FILE_TOO_LARGE: 'El archivo es demasiado grande. Máximo 10MB.',
    INVALID_FILE_TYPE: 'Tipo de archivo no válido. Solo se permiten imágenes.',
    NETWORK_ERROR: 'Error de conexión. Verifica tu internet.',
    QUOTA_EXCEEDED: 'Has excedido el límite de subidas. Intenta más tarde.'
  }
};

// Validar configuración
const validateConfig = () => {
  if (GOOGLE_DRIVE_CONFIG.CLIENT_ID === 'TU_CLIENT_ID_AQUI.apps.googleusercontent.com') {
    console.warn('⚠️ CONFIGURACIÓN PENDIENTE: Debes reemplazar CLIENT_ID con tu Client ID real de Google Cloud Console');
    return false;
  }
  return true;
};

// Exportar configuración
window.GOOGLE_DRIVE_CONFIG = GOOGLE_DRIVE_CONFIG;
window.validateDriveConfig = validateConfig;