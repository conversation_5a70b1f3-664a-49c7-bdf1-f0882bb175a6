/**
 * Sistema de Autenticación Google Drive OAuth 2.0
 * Maneja la autenticación y autorización para acceder a Google Drive API
 */

class GoogleDriveAuth {
  constructor() {
    this.isInitialized = false;
    this.isSignedIn = false;
    this.authInstance = null;
    this.currentUser = null;
    this.accessToken = null;

    // Callbacks para eventos
    this.onAuthStateChange = null;
    this.onError = null;
  }

  /**
   * Inicializa la autenticación de Google
   */
  async initialize() {
    try {
      // Validar configuración
      if (!window.validateDriveConfig()) {
        throw new Error('Configuración de Google Drive incompleta');
      }

      // Cargar Google API
      await this.loadGoogleAPI();

      // Inicializar gapi
      await new Promise((resolve, reject) => {
        gapi.load('auth2', {
          callback: resolve,
          onerror: reject
        });
      });

      // Configurar autenticación
      this.authInstance = await gapi.auth2.init({
        client_id: GOOGLE_DRIVE_CONFIG.CLIENT_ID,
        scope: GOOGLE_DRIVE_CONFIG.SCOPES,
        immediate: false
      });

      // Cargar cliente de Drive API
      await gapi.client.load(GOOGLE_DRIVE_CONFIG.DISCOVERY_DOC);

      // Configurar listeners
      this.setupAuthListeners();

      // Verificar estado inicial
      this.updateAuthState();

      this.isInitialized = true;
      console.log('✅ Google Drive Auth inicializado correctamente');

    } catch (error) {
      console.error('❌ Error inicializando Google Drive Auth:', error);
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Carga la API de Google de forma asíncrona
   */
  loadGoogleAPI() {
    return new Promise((resolve, reject) => {
      if (typeof gapi !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.onload = resolve;
      script.onerror = () => reject(new Error('Error cargando Google API'));
      document.head.appendChild(script);
    });
  }

  /**
   * Configura los listeners de cambio de estado de autenticación
   */
  setupAuthListeners() {
    this.authInstance.isSignedIn.listen((isSignedIn) => {
      this.isSignedIn = isSignedIn;
      this.updateAuthState();

      if (this.onAuthStateChange) {
        this.onAuthStateChange(isSignedIn);
      }
    });
  }

  /**
   * Actualiza el estado de autenticación
   */
  updateAuthState() {
    this.isSignedIn = this.authInstance.isSignedIn.get();

    if (this.isSignedIn) {
      this.currentUser = this.authInstance.currentUser.get();
      this.accessToken = this.currentUser.getAuthResponse().access_token;

      // Configurar token para requests de gapi.client
      gapi.client.setToken({
        access_token: this.accessToken
      });

      console.log('✅ Usuario autenticado:', this.currentUser.getBasicProfile().getEmail());
    } else {
      this.currentUser = null;
      this.accessToken = null;
      console.log('ℹ️ Usuario no autenticado');
    }
  }

  /**
   * Inicia el proceso de login
   */
  async signIn() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (this.isSignedIn) {
        console.log('ℹ️ Usuario ya está autenticado');
        return true;
      }

      console.log('🔐 Iniciando proceso de autenticación...');
      await this.authInstance.signIn();

      return this.isSignedIn;

    } catch (error) {
      console.error('❌ Error en signIn:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * Cierra la sesión
   */
  async signOut() {
    try {
      if (!this.isSignedIn) {
        console.log('ℹ️ Usuario no está autenticado');
        return;
      }

      await this.authInstance.signOut();
      console.log('✅ Sesión cerrada correctamente');

    } catch (error) {
      console.error('❌ Error en signOut:', error);
      this.handleError(error);
    }
  }

  /**
   * Verifica si el usuario está autenticado
   */
  isAuthenticated() {
    return this.isSignedIn && this.accessToken;
  }

  /**
   * Obtiene el token de acceso actual
   */
  getAccessToken() {
    if (!this.isAuthenticated()) {
      throw new Error('Usuario no autenticado');
    }
    return this.accessToken;
  }

  /**
   * Obtiene información del usuario actual
   */
  getCurrentUser() {
    if (!this.isAuthenticated()) {
      return null;
    }

    const profile = this.currentUser.getBasicProfile();
    return {
      id: profile.getId(),
      name: profile.getName(),
      email: profile.getEmail(),
      imageUrl: profile.getImageUrl()
    };
  }

  /**
   * Maneja errores de autenticación
   */
  handleError(error) {
    let userMessage = GOOGLE_DRIVE_CONFIG.MESSAGES.AUTH_REQUIRED;

    if (error.error === 'popup_blocked_by_browser') {
      userMessage = 'El navegador bloqueó la ventana de autenticación. Permite popups para este sitio.';
    } else if (error.error === 'access_denied') {
      userMessage = 'Acceso denegado. Necesitas autorizar el acceso a Google Drive.';
    } else if (error.error === 'network_error') {
      userMessage = GOOGLE_DRIVE_CONFIG.MESSAGES.NETWORK_ERROR;
    }

    if (this.onError) {
      this.onError(error, userMessage);
    }
  }

  /**
   * Configura callback para cambios de estado de autenticación
   */
  setAuthStateChangeCallback(callback) {
    this.onAuthStateChange = callback;
  }

  /**
   * Configura callback para errores
   */
  setErrorCallback(callback) {
    this.onError = callback;
  }
}

// Crear instancia global
window.googleDriveAuth = new GoogleDriveAuth();