<head>
  <script src="https://cdn.ckeditor.com/ckeditor5/38.0.0/classic/ckeditor.js"></script>
</head>
<style>
  * {
    box-sizing: border-box;
  }

  .entry-title {
    display: block !important;
	text-align: center;
  }

  .seminar__list {
    list-style: none;
    margin-top: 20px;
    width: 100%;
  }

  .seminar__sublist {
    border-radius: 5px;
    border: solid 1px;
    margin-top: 10px;
    padding: 20px;
  }

  .seminar__sublist li {
    list-style: none;
    margin-top: 5px;
  }

  .hidden {
    display: none;
  }

  .seminar__sublist:not(.hidden) {
    animation: fade-in 0.3s ease-in-out forwards;
  }

  .seminar__button--toggle {
    cursor: pointer;
  }

  .seminar__field {
    background: none;
    border-radius: 8px;
    border: solid 1.5px #000000;
    color: #ffffff;
    padding: 10px;
  }

  .seminar__fields {
    display: flex;
  }

  .seminar__sublist input,
  .seminar__sublist button {
    margin-right: 5px;
  }

  .seminar__list>.seminar__field {
    width: calc(100% - 260px);
  }

  .seminar__button--save,
  .seminar__button--toggle,
  .seminar__button-list--add,
  .seminar__button-list--delete,
  .seminar__button-sub-list--delete,
  .seminar__button-sub-list--note,
  .seminar__button-sub-seminar--add,
  .seminar__button-list--view {
    background: transparent;
    border-radius: 5px;
    border: none;
    background-color: #3f51b5;
    color: #ffffff;
    cursor: pointer;
    padding: 10px;
    text-transform: capitalize;
  }

  .seminar__button-sub-seminar {
    text-align: right;
    margin-bottom: 20px;
  }

  .seminar__button--save {
    background-color: #4caf50;
  }

  .seminar__button--toggle {
    background-color: #009688;
  }

  .seminar__button-sub-list--delete,
  .seminar__button-list--delete {
    background-color: #f44336;
  }

  .seminar__control {
    display: flex;
    justify-content: end;
  }

  .seminar__control-buttons {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }

  .seminar__lists {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    margin-bottom: 50px;
    margin-top: 50px;
  }

  .modal {
    align-items: center;
    background-color: rgba(0, 0, 0, 0.4);
    display: none;
    height: 100%;
    justify-content: center;
    left: 0;
    overflow: auto;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999999999;
  }

  .modal__content h1,
  .modal__content h2,
  .modal__content h3,
  .modal__content h4,
  .modal__content h5,
  .modal__content h6 {
    color: white;
  }

  .modal__container {
    background-color: #fefefe;
    border: 1px solid #888;
    display: flex;
    flex-direction: column;
    max-width: 800px;
    padding: 20px;
    width: 80%;
  }

  .modal__button--close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }

  .modal__button--close:hover,
  .modal__button--close:focus {
    color: white;
    text-decoration: none;
    cursor: pointer;
  }

  input.seminar__field {
    color: black;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
</style>
<div class="seminar">
  <div class="seminar__control">
    <div class="seminar__control-buttons">
      <button class="seminar__button-list--add" onclick="addNewseminar()">
        Crear un nuevo slide
      </button>
      <button class="seminar__button--save" onclick="saveData()">
        Guardar
      </button>
    </div>
  </div>
  <ul class="seminar__lists">
    <!-- Existing seminars will be added here -->
  </ul>
  <div class="modal">
    <div class="modal__container">
      <div class="modal__button">
        <strong class="modal__title"></strong>
        <span class="modal__button--close" onclick="closeModal()">&times;</span>
      </div>
      <div class="modal__content"></div>
    </div>
  </div>
</div>
<script>
  const CLASS_NAME = 'seminar';
  const CLASS_NAME_LIST = `${CLASS_NAME}__list`;
  const CLASS_NAME_CREATED = `${CLASS_NAME}__list--created`;
  const CLASS_NAME_UPDATED = `${CLASS_NAME}__list--updated`;
  const CLASS_NAME_VIEW = `${CLASS_NAME}__list--view`;
  const CLASS_NAME_FIELD = `${CLASS_NAME}__field`;
  const CLASS_NAME_FIELDS = `${CLASS_NAME_FIELD}s`;
  const CLASS_NAME_SUBLIST = `${CLASS_NAME}__sublist`;
  const TARGET = document.querySelector(`.${CLASS_NAME}__lists`);
  const loading = { color: '#daa521', target: TARGET };
  const deleted = [];
  let editor = {};
  let keywordEvent = null;

  const getInputTemplate = ({
    name = '',
    placeholder = '',
    type = 'text',
    value = ''
  } = {}) => `
    <input
      class="${CLASS_NAME_FIELD}"
      name="${name}"
      oninput="onChange(this)"
      placeholder="${placeholder}"
      type="${type}"
      value="${value}"
    />
  `;

  const getButtonTemplate = ({
    className = '',
    onClick = '',
    value = ''
  } = {}) => `
    <button
      class="${CLASS_NAME}__${className}"
      onclick="${onClick}"
    >
      ${value}
    </button>
   `;

  const getFieldTemplate = ({ imgUrl = '', note = '', subTitle = '', } = {}) => `
    <li class="${CLASS_NAME_FIELDS}">
      ${getInputTemplate({
        placeholder: 'Sub title',
        value: subTitle,
        name: 'subtitle',
      })}
      ${getInputTemplate({
        placeholder: 'Google Drive image id',
        value: imgUrl,
        name: 'image-url',
      })}
      ${getInputTemplate({
        placeholder: 'Note',
        type: 'hidden',
        value: note,
        name: 'note',
      })}
      ${getButtonTemplate({
        className: 'button-sub-list--note',
        onClick: 'openNote(this)',
        value: 'Coach\'s Notes',
      })}
      ${getButtonTemplate({
        className: 'button-sub-list--delete',
        onClick: 'onDeleteSubList(this)',
        value: 'delete',
      })}
    </li>
  `;

  const getseminarTemplate = ({
    id = '',
    input = '',
    isHidden = true,
    title = '',
  } = {}) => `
    ${getInputTemplate({
      placeholder: 'Seminar Name',
      value: title,
      name: 'title',
    })}
    ${getInputTemplate({ type: 'hidden', value: id, name: 'id' })}
    ${getButtonTemplate({
      className: 'button--toggle',
      onClick: 'toggleSubseminar(this)',
      value: 'open',
    })}
    ${getButtonTemplate({
      className: 'button-list--view',
      onClick: 'openView(this)',
      value: 'SlideShow',
    })}
    ${getButtonTemplate({
      className: 'button-list--delete',
      onClick: 'onDeleteList(this)',
      value: 'Delete',
    })}
    <ul class="${CLASS_NAME}__sublist ${isHidden ? 'hidden' : ''}">
	  <li class="${CLASS_NAME}__button-sub-seminar">
	    ${getButtonTemplate({
		  className: 'button-sub-seminar--add',
		  onClick: 'addSubList(this)',
		  value: 'Add new Slide',
	    })}
	  </li>
	  ${input}
    </ul> 
  `;

  const addClassUpdated = (el) =>
    el.classList.contains(CLASS_NAME_LIST) &&
    !el.classList.contains(CLASS_NAME_CREATED) &&
    el.classList.add(CLASS_NAME_UPDATED);

  function modal(behavior) {
    const modal = document.querySelector('.modal');

    modal.style.display = behavior === 'open' ? 'flex' : 'none';
  }

  function openView(target) {
    if (isEmptyInputs()) { return; }
    target.parentNode.classList.add(CLASS_NAME_VIEW);

    const render = document.querySelector('.modal__content');
    const data = extractData('view');

    render.innerHTML = slideShow(data[0]);

    modal('open');
  }

  function openNote(target) {
    const note = target.parentNode.querySelector(`.${CLASS_NAME_FIELD}[name="note"]`);
    const render = document.querySelector('.modal__content');
    const container = document.createElement('textarea');
    const title = document.querySelector('.modal__title');
    const subtitle = target.parentNode.querySelector(`.${CLASS_NAME_FIELD}[name="subtitle"]`).value;

    container.className = 'modal__note';

    render.appendChild(container);
    title.innerText = subtitle;

    ClassicEditor.create(container)
      .then((newEditor) => {
        editor.ClassicEditor = newEditor;
        editor.ClassicEditor.setData(note.value);
        editor.note = {
          update: () => {
            const parent = target.parentNode.parentNode.parentNode;
            addClassUpdated(parent);
            note.value = editor.ClassicEditor.getData();
          },
        };
      })
      .catch((error) => {
        console.error(error);
      });

    modal('open');
  }

  function closeModal(target) {
    const content = document.querySelector('.modal__content');
    const el = document.querySelector(`.${CLASS_NAME_VIEW}`);

    content.innerHTML = '';

    editor.note && editor.note.update();
    editor = {};
    el && el.classList.remove(CLASS_NAME_VIEW);

    window.removeEventListener('keydown', keywordEvent);
    modal('close');
  }

  function onDeleteList(target) {
    const list = target.parentNode;
    const id = list.querySelector(`.${CLASS_NAME_FIELD}[name="id"]`);
    const canRemove = window.confirm('Are you sure you want to remove this seminar?');

    if (canRemove) {
      deleted.push(id.value.trim());
      list.remove();
    }
  }

  function onDeleteSubList(target) {
    const list = target.parentNode;
    const parent = target.parentNode.parentNode.parentNode;
    const canRemove = window.confirm('Are you sure you want to remove this presentation?');

    if (canRemove) {
      addClassUpdated(parent);
      list.remove();
    }
  }

  function onChange(target) {
    const sublist = target.parentNode.parentNode.parentNode;
    const list = target.parentNode;

    addClassUpdated(sublist) || addClassUpdated(list);
  }

  function addNewseminar() {
    if (isEmptyInputs()) { return; }
    const newseminar = document.createElement('li');

    hiddenAllseminar();
    newseminar.className = `${CLASS_NAME_LIST} ${CLASS_NAME_CREATED}`;
    newseminar.innerHTML = getseminarTemplate({ isHidden: false, input: getFieldTemplate() });
    TARGET.insertBefore(newseminar, TARGET.firstChild);
  }

  function addSubList(button) {
    if (isEmptyInputs()) { return; }
    const parentLi = button.parentNode.parentNode;
    const li = document.createElement('li');
    const sublist = parentLi;

    li.className = CLASS_NAME_FIELDS;
    li.innerHTML = getFieldTemplate();

    parentLi.appendChild(li.firstElementChild, parentLi.children[1]);
  }

  function toggleSubseminar(button) {
    if (isEmptyInputs()) { return; }
    const tab = button.parentNode.querySelector(`.${CLASS_NAME_SUBLIST}`);

    hiddenAllseminar();
    tab.classList.toggle('hidden');
  }

  function hiddenAllseminar() {
    const tabs = document.querySelectorAll(`.${CLASS_NAME_SUBLIST}:not(.hidden)`) || [];

    tabs.forEach((tab) => tab.classList.add('hidden'));
  }

  const formatNote = (value) =>
    value.replace(/"/g, '&quot;');

  function extractData(event) {
    const data = [];

    TARGET.querySelectorAll(`.seminar__list--${event}`).forEach(
      (seminarItem) => {
        const getField = (el, name) => {
          const field = el.querySelector(`.${CLASS_NAME_FIELD}[name="${name}"]`);

          return field ? formatNote(field.value.trim()) : '';
        };

        const slideShow = [];
        const id = getField(seminarItem, 'id');

        seminarItem
          .querySelectorAll(`.${CLASS_NAME_FIELDS}`)
          .forEach((subseminarItem) => {
            slideShow.push({
              subTitle: getField(subseminarItem, 'subtitle'),
              imgUrl: getField(subseminarItem, 'image-url'),
              note: getField(subseminarItem, 'note'),
            });
          });

        data.push({
          ...(event === 'updated' && id ? { id } : {}),
          title: getField(seminarItem, 'title').replace(/[^\w ]/g, ''),
          slideShow,
        });
      }
    );

    return data;
  }

  async function saveData() {
    if (isEmptyInputs()) { return; }
    const updated = extractData('updated');
    const created = extractData('created');

    async function send({ data, type, method }) {
      for (let _data of data) {
	    await window.tool.send({ endpoint: 'seminar-slideshow', data: { [type]: _data }})[method]();
      }
    }

    if (updated.length) {
      await send({ data: updated, type: 'updated', method: 'put' });
    }

    if (created.length) {
      await send({ data: created, type: 'created', method: 'post' });
    }

    if (deleted.length) {
      await send({ data: deleted, type: 'deleted', method: 'del' });
    }

    await updateList();
  }

  function isEmptyInputs() {
    const inputs = document.querySelectorAll(`.${CLASS_NAME_FIELD}[type="text"]`);
    const isEmpty = [...inputs].some((input) => input.value.trim() === '');

    isEmpty && alert('Por favor, complete todos los campos antes de agregar un subseminario.');

    return isEmpty;
  }

  async function updateList() {
    const { data = [] } = await window.tool
      .send({ endpoint: 'seminar-slideshow', loading })
      .get();

    TARGET.innerHTML = '';

    data.forEach(({ id, title, slideShow = [] }) => {
      const newseminar = document.createElement('li');
      const input = JSON.parse(slideShow).reduce(
        (current, item) => current + getFieldTemplate(item),
        ''
      );

      newseminar.className = CLASS_NAME_LIST;
      newseminar.innerHTML = getseminarTemplate({ id, title, input });
      TARGET.appendChild(newseminar);
    });
  };

  updateList();
</script>
<style>
  body {
    height: 0;
  }

  .slideshow__container-img {
    height: 500px;
  }

  .slideshow__img--full-screen {
    height: calc(100vh - 30px);
  }

  img.slideshow__img {
    display: none;
    height: 100%;
    width: 758px;
  }

  img.slideshow__img.show {
    display: block;
  }

  .slideshow {
    display: flex;
    justify-content: center;
  }

  .slideshow__container {
    align-items: center;
    display: flex;
    font-family: Arial, Helvetica, sans-serif;
    justify-content: center;
    position: relative;
  }

  .slideshow__content,
  .slideshow__container,
  img.slideshow__img {
    width: 100%;
  }

  .slideshow__next,
  .slideshow__prev {
    color: white;
    position: absolute;
  }

  .slideshow__next {
    right: -80px;
  }

  .slideshow__prev {
    left: -80px;
  }

  .slideshow__control {
    background: #272727;
    color: white;
    display: flex;
    justify-content: space-around;
    padding: 10px;
  }

  .slideshow__button--fullscreen,
  .slideshow__button--note {
    cursor: pointer;
  }
</style>
<script>
  let tab = null;
  const STORAGE = 'EVENT.PUB';

  /**
   * Cleans up the storage by removing the stored data.
   */
  const cleanStorage = () => {
    localStorage.removeItem(STORAGE);
  };

  /**
   * Opens a new browser tab with specified properties.
   * @param {object} tabProperties - An object containing properties for the new tab.
   * @param {string} tabProperties.id - The ID of the new tab.
   * @param {number} tabProperties.index - The index of the new tab.
   * @param {string[]} tabProperties.srcs - An array of source URLs to be loaded in the new tab.
   * @param {string} tabProperties.title - The title of the new tab.
   */
    const openNewTab = ({ id, index, srcs, title }) => {
      if (tab === null) {
        const script = document.createElement('script');
        const TAB_WIDTH = 1000;
        const TAB_HEIGHT = 800
        const CLASS_NAME = 'tab';

        tab = window.open(
          'about:blank',
          '_blank',
          `width=${TAB_WIDTH}, height=${TAB_HEIGHT}`
        );

        const template = `
            <!DOCTYPE html>
            <html lang="en">
            <head>
              <meta charset="UTF-8"/>
              <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
              <title>Coach\'s Notes</title>
              <style>
                body {
                  padding: 0;
                  margin: 0;
                }

                .${CLASS_NAME}__container {
                  align-items: center;
                  display: flex;
                  height: 100vh;
                  justify-content: center;
                  width: 100%;
                }

                .${CLASS_NAME}__img-current {
                  height: 100%;
                  width: 100%;
                }
              </style>
            </head>
            <body>
              <main>
                <div class="${CLASS_NAME}__container">
                  <img class="${CLASS_NAME}__img-current" />
                </div>
              </main>
            </body>
            </html>
          `;

        script.innerHTML = `
            const $ = (className) => document.querySelector('.${CLASS_NAME}__' + className);
            let data = {};
  
            const getImage = (id) =>
              'https://drive.google.com/thumbnail?id=' + id + '&sz=w10000';
  
            const addThreeDots = (text, maxLength) =>
              text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  
            const update = async ({ index, title, srcs }) => {
              const current = $('img-current');

              current.src = getSrc(srcs, index);
            };

            const getSrc = (srcs, index) =>
              srcs[index]
                ? getImage(srcs[index].imgUrl)
                : '';
  
            const sendDataToSlideShow = (data) => {
              localStorage.removeItem('${STORAGE}');
              localStorage.setItem('${STORAGE}', JSON.stringify({ data }));
            };
  
            const onStorage = ({ newValue, key }) => {
              const request = JSON.parse(newValue);
  
              if (request && key === '${STORAGE}') {    
                data = request.data;
  
                update(data);
              }
            };

            window.addEventListener('storage', onStorage);
        `;

        const onClose = () => {
          tab.removeEventListener('beforeunload', onClose);
          tab = null;
          cleanStorage();
        };

        tab.document.open();
        tab.document.write(template);
        tab.document.body.appendChild(script);
        tab.addEventListener('beforeunload', onClose);
        tab.document.close();

        sendDataToTab({ id, index, srcs, title });
      } else {
        alert('You already have a new presentation opened.');
      }
    };

  /**
   * Generate a Google Drive thumbnail image URL with the provided ID.
   * @param {string} id - The ID of the image in Google Drive.
   * @returns {string} The URL of the thumbnail image.
   */
  const getSrc = (id) =>
    `https://drive.google.com/thumbnail?id=${id}&sz=w10000`;


  const sendDataToTab = (data) => {
    cleanStorage();
    localStorage.setItem(STORAGE, JSON.stringify({ data }));
  };

  /**
  * Creates a slideshow with specified properties.
  * @param {object} slideshowProperties - An object containing properties for the slideshow.
  * @param {string} slideshowProperties.id - The ID of the slideshow.
  * @param {string} slideshowProperties.title - The title of the slideshow.
  * @param {string[]} [slideshowProperties.slideShow=[]] - An array of source URLs for the slideshow slides.
  */
  const slideShow = ({ title, slideShow: srcs = [] }) => {
    if (srcs.length === 0) {
      alert("You don't have any presentations to show.");
      return;
    }

    let index = 0;
    const SIZE = srcs.length - 1;
    const CLASS_NAME = 'slideshow';
    const CLASS_NAME_FULL_SCREEN = `${CLASS_NAME}__img--full-screen`;

    /**
     * Retrieves DOM element(s) with the specified class name.
     * @param {string} className - The class name of the element(s) to retrieve.
     * @param {boolean} [isAll=false] - Specifies whether to retrieve all matching elements (default: false).
     * @returns {(Element|NodeList)} Returns the single matching element or a list of matching elements.
     */
    const $ = (className, isAll = false) => {
      const TARGET = `.modal .${CLASS_NAME}__${className}`;

      return isAll ? document.querySelectorAll(TARGET)
        : document.querySelector(TARGET);
    }

    requestAnimationFrame(() => {
      const imgContainer = $('container-img');
      const container = $('container');
      const buttonNext = $('button--next');
      const buttonPrev = $('button--prev');
      const buttonNote = $('button--note');
      const buttonFullScreen = $('button--fullscreen');
      const images = [...$('img', true)];
      const OPACITY = 0;

      buttonPrev.style.opacity = OPACITY;

      window.tool.loading.init({
        ...loading,
        target: document.querySelector('.modal__loading'),
      });

      images.forEach((image, index) => {
        image.removeAttribute('decoding');
        index === 0 && image.classList.add('show');
      });

      const interval = setInterval(() => {
        if (images[0].naturalWidth > 0) {
          window.tool.loading.remove();
          container.removeAttribute('style');
          clearInterval(interval);
        }
      });

      /**
       * Checks if it is possible to navigate to the next slide.
       * @returns {boolean} Returns true if it is possible to navigate to the next slide, otherwise false.
       */
      const canNext = () => index < SIZE;

      /**
       * Checks if it is possible to navigate to the previous slide.
       * @returns {boolean} Returns true if it is possible to navigate to the previous slide, otherwise false.
       */
      const canPrev = () => index > 0;

      /**
       * Sets the opacity of the previous and next buttons based on the ability to navigate to previous and next slides.
       * @return {void}
       */
      const setOPacityArrow = () => {
        buttonPrev.style.opacity = canPrev() ? 1 : OPACITY;
        buttonNext.style.opacity = canNext() ? 1 : OPACITY;
      }

      /**
       * Updates the slideshow to display the slide corresponding to the specified index.
       * @param {number} index - The index of the slide to display.
       * @return {void}
       */
      const update = (index) => {
        const pagination = $('pagination-value');
        const currentImage = $('img.show');
        const imageSelected = $(`img:nth-child(${index + 1})`);

        currentImage.classList.remove('show');
        imageSelected.classList.add('show');
        pagination.innerText = `Page: ${index + 1} / ${SIZE + 1}`;

        sendDataToTab({ index, title, srcs });
        setOPacityArrow();
      };

      buttonNext.addEventListener('click', (event) => {
        canNext() && update(++index);
      });

      buttonPrev.addEventListener('click', (event) => {
        canPrev() && update(--index);
      });

      buttonNote.addEventListener('click', (event) => {
        tab && tab.close();

        openNewTab({ index, title, srcs });
      });

      buttonFullScreen.addEventListener('click', (event) => {
        if (!document.fullscreenElement) {
          container.requestFullscreen();
          imgContainer.classList.add(CLASS_NAME_FULL_SCREEN);
        } else {
          document.exitFullscreen && document.exitFullscreen();
          imgContainer.classList.remove(CLASS_NAME_FULL_SCREEN);
        }
      });

      document.addEventListener('fullscreenchange', (event) => {
        !document.fullscreenElement && imgContainer.classList.remove(CLASS_NAME_FULL_SCREEN);
      });

      window.addEventListener('storage', (event) => {
        const request = event.newValue;

        if (request && event.key === STORAGE && tab) {
          const newData = JSON.parse(request);

          index = Number(newData.data.index);
          update(index);
        }
      });

      window.addEventListener('keydown',  ({ key }) => {
        if (key === 'ArrowLeft' && canPrev()) {
          update(--index);
        } else if (key === 'ArrowRight' && canNext()) {
          update(++index);
        }
      });

      $('container-img').addEventListener('click', () => {
        canNext() && update(++index);
      });
    });

    return `
    <div class="modal__loading"></div>
    <div class="${CLASS_NAME}">
      <div class="${CLASS_NAME}__container" style="display: none;">
        <div class="${CLASS_NAME}__prev">
          <span type="button" class="${CLASS_NAME}__button--prev">
            &#10094;
          </span>
        </div>
        <div class="${CLASS_NAME}__content">
          <div class="${CLASS_NAME}__container-img">
            ${tool.template(srcs, ({ imgUrl }) =>
              `<img class="${CLASS_NAME}__img" src="${getSrc(imgUrl)}"/>`
            )}
          </div>
          <div class="${CLASS_NAME}__control">
            <div class="${CLASS_NAME}__pagination">
              <span class="${CLASS_NAME}__pagination-value">
                Page: 1 * ${SIZE + 1}
              </span>
            </div>
            <div class="${CLASS_NAME}__note">
              <span class="${CLASS_NAME}__button--note">
                Slide for Student
              </span>
            </div>
            <div class="${CLASS_NAME}__fullscreen">
              <span class="${CLASS_NAME}__button--fullscreen">
				Full screen
			  </span>
            </div>
          </div>
        </div>
        <div class="${CLASS_NAME}__next">
          <span type="button" class="${CLASS_NAME}__button--next"> 
            &#10095;
          </span>
        </div>
      </div>
    </div>
  `;
  };
</script>