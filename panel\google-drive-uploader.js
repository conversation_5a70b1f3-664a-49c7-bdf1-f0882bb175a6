/**
 * Clase principal para subir archivos a Google Drive
 * Maneja la subida de archivos, creación de carpetas y gestión de permisos
 */

class GoogleDriveUploader {
  constructor() {
    this.folderCache = new Map();
    this.uploadQueue = [];
    this.activeUploads = 0;
    this.maxConcurrentUploads = GOOGLE_DRIVE_CONFIG.UPLOAD_CONFIG.MAX_CONCURRENT_UPLOADS;

    // Callbacks para eventos
    this.onUploadProgress = null;
    this.onUploadComplete = null;
    this.onUploadError = null;
  }

  /**
   * Valida un archivo antes de subirlo
   */
  validateFile(file) {
    const config = GOOGLE_DRIVE_CONFIG.FILE_CONFIG;

    // Verificar tipo de archivo
    if (!config.ALLOWED_TYPES.includes(file.type)) {
      throw new Error(GOOGLE_DRIVE_CONFIG.MESSAGES.INVALID_FILE_TYPE);
    }

    // Verificar tamaño
    if (file.size > config.MAX_FILE_SIZE) {
      throw new Error(GOOGLE_DRIVE_CONFIG.MESSAGES.FILE_TOO_LARGE);
    }

    return true;
  }

  /**
   * Comprime una imagen antes de subirla
   */
  async compressImage(file) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        const config = GOOGLE_DRIVE_CONFIG.FILE_CONFIG;
        let { width, height } = img;

        // Calcular nuevas dimensiones si excede el máximo
        if (width > config.MAX_DIMENSION || height > config.MAX_DIMENSION) {
          const ratio = Math.min(
            config.MAX_DIMENSION / width,
            config.MAX_DIMENSION / height
          );
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Dibujar imagen redimensionada
        ctx.drawImage(img, 0, 0, width, height);

        // Convertir a blob con compresión
        canvas.toBlob(
          resolve,
          'image/jpeg',
          config.COMPRESSION_QUALITY
        );
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Busca o crea una carpeta en Google Drive
   */
  async getOrCreateFolder(name, parentId = null) {
    const cacheKey = `${name}_${parentId || 'root'}`;

    // Verificar cache
    if (this.folderCache.has(cacheKey)) {
      return this.folderCache.get(cacheKey);
    }

    try {
      // Buscar carpeta existente
      const searchQuery = parentId
        ? `name='${name}' and '${parentId}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false`
        : `name='${name}' and mimeType='application/vnd.google-apps.folder' and trashed=false`;

      const searchResponse = await gapi.client.drive.files.list({
        q: searchQuery,
        spaces: 'drive',
        fields: 'files(id, name)'
      });

      if (searchResponse.result.files.length > 0) {
        const folder = searchResponse.result.files[0];
        this.folderCache.set(cacheKey, folder);
        return folder;
      }

      // Crear nueva carpeta
      const folderMetadata = {
        name: name,
        mimeType: 'application/vnd.google-apps.folder',
        ...(parentId && { parents: [parentId] })
      };

      const createResponse = await gapi.client.drive.files.create({
        resource: folderMetadata,
        fields: 'id, name'
      });

      const newFolder = createResponse.result;
      this.folderCache.set(cacheKey, newFolder);

      console.log(`✅ Carpeta creada: ${name} (${newFolder.id})`);
      return newFolder;

    } catch (error) {
      console.error(`❌ Error creando carpeta ${name}:`, error);
      throw error;
    }
  }

  /**
   * Crea la estructura de carpetas para un seminario
   */
  async createSeminarFolderStructure(seminarName) {
    const config = GOOGLE_DRIVE_CONFIG.FOLDER_CONFIG;

    try {
      // Carpeta base
      const baseFolder = await this.getOrCreateFolder(config.BASE_FOLDER_NAME);

      let currentFolder = baseFolder;

      // Carpeta por fecha si está habilitada
      if (config.CREATE_DATE_FOLDERS) {
        const dateString = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        currentFolder = await this.getOrCreateFolder(dateString, baseFolder.id);
      }

      // Carpeta por seminario si está habilitada
      if (config.CREATE_SEMINAR_FOLDERS && seminarName) {
        const cleanSeminarName = seminarName.replace(/[^\w\s-]/g, '').trim();
        currentFolder = await this.getOrCreateFolder(cleanSeminarName, currentFolder.id);
      }

      return currentFolder;

    } catch (error) {
      console.error('❌ Error creando estructura de carpetas:', error);
      throw error;
    }
  }

  /**
   * Sube un archivo a Google Drive
   */
  async uploadFile(file, seminarName = null, options = {}) {
    try {
      // Verificar autenticación
      if (!window.googleDriveAuth.isAuthenticated()) {
        throw new Error(GOOGLE_DRIVE_CONFIG.MESSAGES.AUTH_REQUIRED);
      }

      // Validar archivo
      this.validateFile(file);

      // Comprimir imagen si es necesario
      const processedFile = await this.compressImage(file);

      // Crear estructura de carpetas
      const targetFolder = await this.createSeminarFolderStructure(seminarName);

      // Preparar metadatos del archivo
      const fileName = `${Date.now()}_${file.name}`;
      const metadata = {
        name: fileName,
        parents: [targetFolder.id],
        description: `Uploaded from Slideshow Panel for seminar: ${seminarName || 'Unknown'}`
      };

      // Subir archivo
      const uploadResult = await this.performUpload(processedFile, metadata, options);

      console.log(`✅ Archivo subido: ${fileName} (${uploadResult.id})`);

      // Callback de éxito
      if (this.onUploadComplete) {
        this.onUploadComplete({
          fileId: uploadResult.id,
          fileName: fileName,
          originalFile: file,
          seminarName: seminarName,
          folderId: targetFolder.id
        });
      }

      return {
        id: uploadResult.id,
        name: fileName,
        folderId: targetFolder.id,
        webViewLink: uploadResult.webViewLink,
        thumbnailLink: `https://drive.google.com/thumbnail?id=${uploadResult.id}&sz=w1920`
      };

    } catch (error) {
      console.error('❌ Error subiendo archivo:', error);

      if (this.onUploadError) {
        this.onUploadError(error, file);
      }

      throw error;
    }
  }

  /**
   * Realiza la subida del archivo usando resumable upload
   */
  async performUpload(file, metadata, options = {}) {
    const boundary = '-------314159265358979323846';
    const delimiter = "\r\n--" + boundary + "\r\n";
    const close_delim = "\r\n--" + boundary + "--";

    // Crear cuerpo multipart
    const metadataString = JSON.stringify(metadata);
    const multipartRequestBody =
      delimiter +
      'Content-Type: application/json\r\n\r\n' +
      metadataString +
      delimiter +
      'Content-Type: ' + file.type + '\r\n\r\n';

    // Crear request
    const request = new XMLHttpRequest();

    return new Promise((resolve, reject) => {
      // Configurar progress tracking
      if (options.onProgress && this.onUploadProgress) {
        request.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            const progress = (e.loaded / e.total) * 100;
            this.onUploadProgress(progress, file);
            if (options.onProgress) {
              options.onProgress(progress);
            }
          }
        });
      }

      request.onload = () => {
        if (request.status === 200) {
          const result = JSON.parse(request.responseText);
          resolve(result);
        } else {
          reject(new Error(`Upload failed: ${request.status} ${request.statusText}`));
        }
      };

      request.onerror = () => {
        reject(new Error(GOOGLE_DRIVE_CONFIG.MESSAGES.NETWORK_ERROR));
      };

      // Configurar request
      request.open('POST', 'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&fields=id,name,webViewLink');
      request.setRequestHeader('Authorization', `Bearer ${window.googleDriveAuth.getAccessToken()}`);
      request.setRequestHeader('Content-Type', 'multipart/related; boundary="' + boundary + '"');

      // Enviar datos
      const requestBody = new Blob([
        multipartRequestBody,
        file,
        close_delim
      ]);

      request.send(requestBody);
    });
  }